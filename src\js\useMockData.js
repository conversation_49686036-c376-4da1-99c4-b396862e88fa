/**
 * 使用模拟数据的工具文件
 * 在开发和测试阶段替换真实接口调用
 */

import mockData, { 
    mockWeatherService, 
    mockSoilService, 
    generateSmartIOTData,
    generateCollectTime 
} from './mockData.js';

// 是否启用模拟数据的开关
const USE_MOCK_DATA = true; // 设置为 false 可以切换回真实接口

/**
 * 在SmartIOTHistoryDialog.vue中使用模拟数据的示例
 * 
 * 使用方法：
 * 1. 在组件中导入这个文件
 * 2. 替换原有的服务调用
 */

// 模拟数据使用示例 - 替换SmartIOTHistoryDialog.vue中的方法
export const mockSmartIOTHistoryMethods = {
    // 替换getAirTHChart方法
    getAirTHChart() {
        if (!USE_MOCK_DATA) {
            // 这里调用原始方法
            return;
        }
        
        if (this.airTHTimeActive == 0) {
            // 本天数据
            mockWeatherService.temperatureChartDay(this.areaId)
                .then(res => {
                    this.$chart.THLineToday(res).then(data => {
                        this.airTHLineOption = data;
                    });
                })
                .catch(err => {
                    console.error('获取空气温湿度本天数据失败:', err);
                    this.airTHLineOption = { show: false, option: {} };
                });
        } else {
            // 近七天或近30天数据
            let po = {
                areaId: this.areaId,
                showType: this.airTHTimeActive
            };
            mockWeatherService.temperatureChart(po)
                .then(res => {
                    if (res.weatherChartVoList == null || res.weatherChartVoList.length == 0) {
                        this.airTHLineOption = {
                            show: false,
                            option: {},
                        };
                    } else {
                        this.$chart.THLine(res.weatherChartVoList).then(data => {
                            this.airTHLineOption = data;
                        });
                    }
                })
                .catch(err => {
                    console.error('获取空气温湿度数据失败:', err);
                    this.airTHLineOption = { show: false, option: {} };
                });
        }
    },

    // 替换getCO2Chart方法
    getCO2Chart() {
        if (!USE_MOCK_DATA) {
            return;
        }
        
        if (this.co2TimeActive == 0) {
            // 本天数据
            mockWeatherService.temperatureChartDay(this.areaId)
                .then(res => {
                    const co2Data = res ? res.map(item => ({
                        time: item.showTime,
                        value: item.carbonDioxide || 0
                    })) : [];
                    this.$chart.CO2Line(co2Data).then(data => {
                        this.co2LineOption = data;
                    });
                })
                .catch(err => {
                    console.error('获取CO2本天数据失败:', err);
                    this.co2LineOption = { show: false, option: {} };
                });
        } else {
            // 近七天或近30天数据
            let po = {
                areaId: this.areaId,
                showType: this.co2TimeActive
            };
            mockWeatherService.temperatureChart(po)
                .then(res => {
                    if (res.weatherChartVoList == null || res.weatherChartVoList.length == 0) {
                        this.co2LineOption = {
                            show: false,
                            option: {},
                        };
                    } else {
                        const co2Data = res.weatherChartVoList.map(item => ({
                            time: item.showTime,
                            value: item.carbonDioxideAvg || 0
                        }));
                        this.$chart.CO2Line(co2Data).then(data => {
                            this.co2LineOption = data;
                        });
                    }
                })
                .catch(err => {
                    console.error('获取CO2数据失败:', err);
                    this.co2LineOption = { show: false, option: {} };
                });
        }
    },

    // 替换getIlluminanceChart方法
    getIlluminanceChart() {
        if (!USE_MOCK_DATA) {
            return;
        }
        
        let po = {
            areaId: this.areaId,
            showType: this.illuminanceActive
        };
        mockWeatherService.illuminanceChart(po)
            .then(res => {
                if (res.illuminanceInfo == null || res.illuminanceInfo.length == 0) {
                    this.illuminanceLineOption = {
                        show: false,
                        option: {},
                    };
                } else {
                    this.$chart.IlluminanceLine(res.illuminanceInfo ? res.illuminanceInfo : []).then(data => {
                        this.illuminanceLineOption = data;
                    });
                }
            })
            .catch(err => {
                console.error('获取光照度数据失败:', err);
                this.illuminanceLineOption = { show: false, option: {} };
            });
    },

    // 替换getSoilTHChart方法
    getSoilTHChart() {
        if (!USE_MOCK_DATA) {
            return;
        }
        
        let po = {
            areaId: this.areaId,
            layer: 0, // 默认使用第一层土壤
            startTime: this.soilTHTimeActive === 1 ? '2024-01-01' : '2023-12-01', // 模拟时间
            endTime: '2024-01-08'
        };
        
        mockSoilService.listChart(po)
            .then(res => {
                if (res == null || res.length == 0) {
                    this.soilTHLineOption = {
                        show: false,
                        option: {},
                    };
                } else {
                    let name = [], soilT = [], soilH = [];
                    res.forEach(e => {
                        name.push(e.showTime);
                        soilT.push(e.value.temperatureAvg);
                        soilH.push(e.value.humidityAvg);
                    });
                    this.$chart.soilTHLine(name, soilT, soilH).then(data => {
                        this.soilTHLineOption = data;
                    });
                }
            })
            .catch(err => {
                console.error('获取土壤温湿度数据失败:', err);
                this.soilTHLineOption = { show: false, option: {} };
            });
    },

    // 替换getConductivityChart方法
    getConductivityChart() {
        if (!USE_MOCK_DATA) {
            return;
        }
        
        let po = {
            areaId: this.areaId,
            layer: 0, // 默认使用第一层土壤
            startTime: this.conductivityActive === 1 ? '2024-01-01' : '2023-12-01', // 模拟时间
            endTime: '2024-01-08'
        };
        
        mockSoilService.listChart(po)
            .then(res => {
                if (res == null || res.length == 0) {
                    this.conductivityLineOption = {
                        show: false,
                        option: {},
                    };
                } else {
                    let name = [], conductivity = [];
                    res.forEach(e => {
                        name.push(e.showTime);
                        conductivity.push(e.value.conductivityAvg);
                    });
                    this.$chart.conductivityLine(name, conductivity).then(data => {
                        this.conductivityLineOption = data;
                    });
                }
            })
            .catch(err => {
                console.error('获取电导率数据失败:', err);
                this.conductivityLineOption = { show: false, option: {} };
            });
    },

    // 替换getWeatherAreaData方法
    async getWeatherAreaData(areaId) {
        if (!USE_MOCK_DATA) {
            return;
        }
        
        if (!areaId) return;
        try {
            const res = await mockWeatherService.weatherAreaData(areaId);
            this.collectDateTime = res.collectTime;
        } catch (err) {
            console.error('获取数据采集时间失败:', err);
            this.collectDateTime = ''; // 失败时清空时间显示
        }
    }
};

// 生成模拟的智慧物联数据
export function generateMockSmartIOTData() {
    return {
        smartIOTData: generateSmartIOTData(),
        collectDateTime: generateCollectTime(),
        areaId: 'mock-area-001',
        areaName: '模拟玉米种植区'
    };
}

// 在控制台输出使用说明
export function showUsageInstructions() {
    console.log('=== 模拟数据使用说明 ===');
    console.log('1. 在SmartIOTHistoryDialog.vue中导入: import { mockSmartIOTHistoryMethods } from "@/js/useMockData.js"');
    console.log('2. 在methods中使用模拟方法替换原有方法');
    console.log('3. 设置USE_MOCK_DATA为true启用模拟数据');
    console.log('4. 可以通过以下方式生成测试数据:');
    console.log('   - generateMockSmartIOTData() // 生成智慧物联数据');
    console.log('   - mockWeatherService.temperatureChartDay("area-001") // 生成温湿度数据');
    console.log('   - mockSoilService.listChart({areaId: "area-001", layer: 0}) // 生成土壤数据');
    console.log('========================');
}

export default {
    USE_MOCK_DATA,
    mockSmartIOTHistoryMethods,
    generateMockSmartIOTData,
    showUsageInstructions,
    mockWeatherService,
    mockSoilService
};
