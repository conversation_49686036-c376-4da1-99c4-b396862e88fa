/**
 * 模拟数据生成器
 * 用于生成智慧农业温室系统的测试数据
 */

// 生成随机数的工具函数
function randomBetween(min, max, decimals = 1) {
    const value = Math.random() * (max - min) + min;
    return Number(value.toFixed(decimals));
}

// 生成时间序列
function generateTimeSequence(type = 'today', count = 24) {
    const times = [];
    const now = new Date();
    
    if (type === 'today') {
        // 生成今天的小时数据 (00:00 - 23:00)
        for (let i = 0; i < count; i++) {
            const hour = String(i).padStart(2, '0');
            times.push(`${hour}:00`);
        }
    } else if (type === 'week') {
        // 生成近7天的数据
        for (let i = 6; i >= 0; i--) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            times.push(`${month}-${day}`);
        }
    } else if (type === 'month') {
        // 生成近30天的数据
        for (let i = 29; i >= 0; i--) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            times.push(`${month}-${day}`);
        }
    }
    
    return times;
}

// 生成智慧物联实时数据
export function generateSmartIOTData() {
    return {
        temperature: randomBetween(18, 28, 1), // 温度 18-28℃
        humidity: randomBetween(45, 75, 1), // 湿度 45-75%RH
        co2: randomBetween(350, 800, 0), // 二氧化碳 350-800ppm
        lightIntensity: randomBetween(20000, 80000, 0), // 光照度 20000-80000lux
        soilTemperature: randomBetween(15, 25, 1), // 土温 15-25℃
        soilHumidity: randomBetween(30, 70, 1), // 土湿 30-70%
        conductivity: randomBetween(200, 1500, 0), // 电导率 200-1500μS/cm
    };
}

// 生成空气温湿度图表数据 - 今天数据
export function generateAirTHChartToday() {
    const times = generateTimeSequence('today', 24);
    return times.map(time => ({
        showTime: time,
        temperature: randomBetween(18, 28, 1),
        humidity: randomBetween(45, 75, 1),
        carbonDioxide: randomBetween(350, 800, 0), // CO2数据也在这里
    }));
}

// 生成空气温湿度图表数据 - 近7天或30天
export function generateAirTHChart(type = 'week') {
    const times = generateTimeSequence(type);
    return {
        temperatureAvg: randomBetween(20, 25, 1),
        temperatureMax: randomBetween(25, 30, 1),
        temperatureMin: randomBetween(15, 20, 1),
        weatherChartVoList: times.map(time => ({
            showTime: time,
            temperatureAvg: randomBetween(18, 28, 1),
            temperatureMax: randomBetween(25, 32, 1),
            temperatureMin: randomBetween(12, 18, 1),
            humidityAvg: randomBetween(45, 75, 1),
            carbonDioxideAvg: randomBetween(350, 800, 0), // CO2平均值
        }))
    };
}

// 生成光照度图表数据
export function generateIlluminanceChart(type = 'today') {
    const times = generateTimeSequence(type);
    return {
        illuminanceAvg: randomBetween(40, 60, 0),
        illuminanceInfo: times.map(time => ({
            showTime: time,
            illuminanceAvg: randomBetween(20000, 80000, 0),
        }))
    };
}

// 生成土壤图表数据
export function generateSoilChart(type = 'week') {
    const times = generateTimeSequence(type);
    return times.map(time => ({
        showTime: time,
        value: {
            temperatureAvg: randomBetween(15, 25, 1), // 土壤温度
            humidityAvg: randomBetween(30, 70, 1), // 土壤湿度
            phAvg: randomBetween(6.0, 8.0, 1), // PH值
            conductivityAvg: randomBetween(200, 1500, 0), // 电导率
            nitrogenAvg: randomBetween(50, 200, 1), // 氮
            phosphorusAvg: randomBetween(20, 80, 1), // 磷
            potassiumAvg: randomBetween(100, 300, 1), // 钾
        }
    }));
}

// 生成数据采集时间
export function generateCollectTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

// 模拟WeatherService接口
export const mockWeatherService = {
    // 模拟temperatureChartDay接口
    temperatureChartDay(areaId) {
        console.log('Mock temperatureChartDay called with areaId:', areaId);
        return Promise.resolve(generateAirTHChartToday());
    },
    
    // 模拟temperatureChart接口
    temperatureChart(po) {
        console.log('Mock temperatureChart called with:', po);
        const type = po.showType === 1 ? 'week' : 'month';
        return Promise.resolve(generateAirTHChart(type));
    },
    
    // 模拟illuminanceChart接口
    illuminanceChart(po) {
        console.log('Mock illuminanceChart called with:', po);
        const type = po.showType === 0 ? 'today' : (po.showType === 1 ? 'week' : 'month');
        return Promise.resolve(generateIlluminanceChart(type));
    },
    
    // 模拟weatherAreaData接口
    weatherAreaData(areaId) {
        console.log('Mock weatherAreaData called with areaId:', areaId);
        return Promise.resolve({
            collectTime: generateCollectTime()
        });
    }
};

// 模拟SoilService接口
export const mockSoilService = {
    // 模拟listChart接口
    listChart(po) {
        console.log('Mock SoilService.listChart called with:', po);
        const type = po.startTime && po.endTime ? 'week' : 'month'; // 简单判断
        return Promise.resolve(generateSoilChart(type));
    },
    
    // 模拟findNewInfo接口
    findNewInfo(layer, areaId) {
        console.log('Mock SoilService.findNewInfo called with layer:', layer, 'areaId:', areaId);
        return Promise.resolve({
            ...generateSmartIOTData(),
            createTime: generateCollectTime(),
            layer: layer
        });
    }
};

// 启用模拟数据的函数
export function enableMockData() {
    // 这里可以替换真实的服务调用
    console.log('Mock data enabled. You can now use the mock services for testing.');
    console.log('Available mock services:');
    console.log('- mockWeatherService');
    console.log('- mockSoilService');
    console.log('- generateSmartIOTData()');
    
    // 示例用法
    console.log('Example usage:');
    console.log('Smart IOT Data:', generateSmartIOTData());
    console.log('Air TH Chart Today:', generateAirTHChartToday());
    console.log('Soil Chart:', generateSoilChart('week'));
}

// 默认导出所有生成器函数
export default {
    generateSmartIOTData,
    generateAirTHChartToday,
    generateAirTHChart,
    generateIlluminanceChart,
    generateSoilChart,
    generateCollectTime,
    mockWeatherService,
    mockSoilService,
    enableMockData
};
